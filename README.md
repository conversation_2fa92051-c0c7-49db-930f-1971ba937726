# 🃏 斗地主游戏 - 线上智力竞技比赛选拔系统

一个基于React + Phaser的现代化斗地主游戏平台，支持在线竞技比赛和排位系统。

## ✨ 功能特性

- 🎮 **经典斗地主玩法** - 完整的斗地主游戏规则
- 🏆 **竞技排位系统** - 瑞士移位赛制，公平竞技
- 📱 **跨平台支持** - 支持Web端，可快速转换为移动端APP
- 🎨 **精美界面** - 现代化UI设计，流畅的游戏体验
- 🔄 **实时通信** - 基于SignalR的实时游戏状态同步
- 👥 **匿名化设计** - 保护用户隐私，匿名显示玩家信息

## 🚀 快速开始

### 环境要求

- Node.js 20.9.0+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

游戏将在 `http://localhost:5173` 启动（端口可能会自动调整）

### 构建生产版本

```bash
npm run build
```

## 🎯 游戏功能

### 当前已实现

- ✅ 游戏主菜单系统
- ✅ 基础游戏界面
- ✅ 玩家位置布局（南、西、东）
- ✅ 基础操作按钮（叫地主、出牌、托管等）
- ✅ 简单的手牌显示
- ✅ 游戏规则说明
- ✅ 场景切换系统

### 开发中功能

- 🔄 完整的牌局逻辑
- 🔄 AI玩家系统
- 🔄 网络对战功能
- 🔄 排行榜系统
- 🔄 用户认证系统

## 🎮 游戏操作

- **开始游戏**: 点击主菜单的"开始游戏"按钮
- **查看规则**: 点击"游戏规则"查看详细玩法
- **返回菜单**: 游戏中点击"返回菜单"或按ESC键
- **游戏操作**: 使用界面按钮进行叫地主、出牌等操作

## 🏗️ 技术架构

### 前端技术栈

- **React 18.2.0** - 现代化前端框架
- **Phaser 3.60.0** - 强大的2D游戏引擎
- **Ant Design 5.0.0** - 企业级UI组件库
- **MobX 6.0.0** - 响应式状态管理
- **Vite 4.4.0** - 快速构建工具

### 后端集成

- **SignalR** - 实时通信
- **.NET Core API** - 后端服务（待对接）
- **MySQL** - 数据存储
- **Redis** - 缓存中间件

## 📁 项目结构

```
src/
├── api/              # API接口层
│   ├── index.js      # 主要API接口
│   └── signalr.js    # SignalR实时通信
├── components/       # 通用组件
│   └── Layout.jsx    # 布局组件
├── game/             # 游戏核心
│   ├── GameScene.js  # 游戏场景
│   └── MenuScene.js  # 菜单场景
├── pages/            # 页面组件
│   ├── GameConfig/   # 游戏配置
│   ├── MonitorDashboard/ # 监控面板
│   ├── RoomMonitor/  # 房间监控
│   └── StatsDashboard/   # 统计面板
├── App.jsx           # 主应用组件
├── App.css           # 主样式文件
└── main.jsx          # 应用入口
```

## 🎲 游戏规则

### 基础规则

1. **三人游戏**: 一人当地主，两人当农民
2. **叫牌阶段**: 轮流叫分（1分、2分、3分或不叫）
3. **出牌规则**: 地主先出，按逆时针轮流出牌
4. **胜利条件**: 最先出完手牌的一方获胜

### 特殊规则

- **炸弹限制**: 三炸封顶，第四炸不再计分
- **托管机制**: 超时自动托管，按规则自动出牌
- **轮空处理**: 非3的倍数人数时的轮空积分规则

## 🔧 开发指南

### 添加新功能

1. 在对应目录创建新组件
2. 更新路由配置（如需要）
3. 添加相应的API接口
4. 编写单元测试

### 调试技巧

- 打开浏览器开发者工具查看控制台日志
- 使用React DevTools检查组件状态
- Phaser游戏调试可在GameScene中启用debug模式

## 📝 更新日志

### v1.0.0 (当前版本)

- ✅ 基础项目架构搭建
- ✅ 游戏引擎集成
- ✅ 主菜单系统
- ✅ 基础游戏界面
- ✅ 实时通信框架

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 开发团队: 斗地主游戏开发组

---

**注意**: 本项目目前处于开发阶段，部分功能仍在完善中。欢迎提供反馈和建议！
