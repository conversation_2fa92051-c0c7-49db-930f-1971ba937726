import Phaser from 'phaser'

export default class MenuScene extends Phaser.Scene {
  constructor() {
    super({ key: 'MenuScene' })
  }

  preload() {
    console.log('MenuScene: preload')
  }

  create() {
    console.log('MenuScene: create')
    
    // 设置背景渐变
    this.cameras.main.setBackgroundColor('#1a472a')
    
    this.createBackground()
    this.createTitle()
    this.createMenu()
    this.createFooter()
  }

  createBackground() {
    // 创建装饰性背景元素
    const graphics = this.add.graphics()
    
    // 添加一些装饰圆圈
    for (let i = 0; i < 10; i++) {
      const x = Phaser.Math.Between(0, 800)
      const y = Phaser.Math.Between(0, 600)
      const radius = Phaser.Math.Between(20, 50)
      const alpha = Phaser.Math.FloatBetween(0.1, 0.3)
      
      graphics.fillStyle(0x2d7a3f, alpha)
      graphics.fillCircle(x, y, radius)
    }
  }

  createTitle() {
    // 主标题
    this.add.text(400, 120, '斗地主', {
      fontSize: '72px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 4
    }).setOrigin(0.5)
    
    // 副标题
    this.add.text(400, 180, '线上智力竞技比赛选拔系统', {
      fontSize: '24px',
      color: '#cccccc',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)
  }

  createMenu() {
    const menuItems = [
      { text: '开始游戏', action: 'startGame', color: '#4CAF50' },
      { text: '游戏规则', action: 'showRules', color: '#2196F3' },
      { text: '排行榜', action: 'showRanking', color: '#FF9800' },
      { text: '设置', action: 'showSettings', color: '#9C27B0' }
    ]

    menuItems.forEach((item, index) => {
      const y = 280 + index * 70
      
      // 按钮背景
      const buttonBg = this.add.graphics()
      buttonBg.fillStyle(parseInt(item.color.replace('#', '0x')))
      buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)
      
      // 按钮文字
      const button = this.add.text(400, y, item.text, {
        fontSize: '28px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        fontStyle: 'bold'
      }).setOrigin(0.5)
      
      // 设置交互
      const interactive = this.add.zone(250, y - 25, 300, 50)
      interactive.setInteractive()
      
      interactive.on('pointerover', () => {
        buttonBg.clear()
        buttonBg.fillStyle(parseInt(item.color.replace('#', '0x')), 0.8)
        buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)
        button.setScale(1.05)
      })
      
      interactive.on('pointerout', () => {
        buttonBg.clear()
        buttonBg.fillStyle(parseInt(item.color.replace('#', '0x')))
        buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)
        button.setScale(1)
      })
      
      interactive.on('pointerdown', () => {
        this.handleMenuClick(item.action)
      })
    })
  }

  createFooter() {
    this.add.text(400, 570, '版本 1.0.0 | 开发中...', {
      fontSize: '16px',
      color: '#666666',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)
  }

  handleMenuClick(action) {
    switch(action) {
      case 'startGame':
        console.log('开始游戏')
        this.scene.start('GameScene')
        break
      case 'showRules':
        this.showRules()
        break
      case 'showRanking':
        this.showMessage('排行榜功能开发中...')
        break
      case 'showSettings':
        this.showMessage('设置功能开发中...')
        break
    }
  }

  showRules() {
    // 创建规则弹窗
    const overlay = this.add.graphics()
    overlay.fillStyle(0x000000, 0.7)
    overlay.fillRect(0, 0, 800, 600)
    
    const panel = this.add.graphics()
    panel.fillStyle(0xffffff)
    panel.fillRoundedRect(100, 100, 600, 400, 10)
    panel.lineStyle(2, 0x333333)
    panel.strokeRoundedRect(100, 100, 600, 400, 10)
    
    this.add.text(400, 130, '游戏规则', {
      fontSize: '32px',
      color: '#333333',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const rules = [
      '1. 三人游戏，一人当地主，两人当农民',
      '2. 地主先出牌，然后按逆时针方向轮流出牌',
      '3. 后出牌者必须比前一家出的牌大',
      '4. 最先出完手中牌的一方获胜',
      '5. 炸弹可以压任何牌型，王炸最大'
    ]
    
    rules.forEach((rule, index) => {
      this.add.text(120, 180 + index * 30, rule, {
        fontSize: '18px',
        color: '#333333',
        fontFamily: 'Arial, sans-serif'
      })
    })
    
    // 关闭按钮
    const closeBtn = this.add.text(400, 450, '关闭', {
      fontSize: '24px',
      color: '#ffffff',
      backgroundColor: '#f44336',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)
    
    closeBtn.setInteractive()
    closeBtn.on('pointerdown', () => {
      overlay.destroy()
      panel.destroy()
      closeBtn.destroy()
      // 销毁所有规则文本
      this.children.list.forEach(child => {
        if (child.type === 'Text' && child.text.includes('游戏规则')) {
          child.destroy()
        }
      })
    })
  }

  showMessage(text) {
    const message = this.add.text(400, 300, text, {
      fontSize: '24px',
      color: '#ffff00',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#000000',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)
    
    this.time.delayedCall(2000, () => {
      message.destroy()
    })
  }
}
