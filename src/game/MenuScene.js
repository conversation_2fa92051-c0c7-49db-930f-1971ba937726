import Phaser from 'phaser'

export default class MenuScene extends Phaser.Scene {
  constructor() {
    super({ key: 'MenuScene' })
  }

  preload() {
    console.log('MenuScene: preload')

    // 加载背景图片
    this.load.image('login_bg', '/bg_login.jpg')
    this.load.image('logo', '/hall_logo_pic.png')

    // 加载按钮
    this.load.image('btn_start', '/UI/bt_start_0.png')
    this.load.image('btn_create_room', '/UI/btn_create_room.png')
    this.load.image('btn_enter_room', '/UI/btn_enter_room.png')

    // 加载音效
    this.load.audio('login_bg_music', '/sound/login_bg.ogg')
  }

  create() {
    console.log('MenuScene: create')

    this.createBackground()
    this.createTitle()
    this.createMenu()
    this.createFooter()

    // 播放背景音乐
    if (this.sound.get('login_bg_music')) {
      this.sound.play('login_bg_music', {
        volume: 0.3,
        loop: true
      })
    }
  }

  createBackground() {
    // 使用真实的登录背景图片
    const bg = this.add.image(400, 300, 'login_bg')
    bg.setDisplaySize(800, 600)

    // 添加半透明遮罩
    const overlay = this.add.graphics()
    overlay.fillStyle(0x000000, 0.4)
    overlay.fillRect(0, 0, 800, 600)
  }

  createTitle() {
    // 主标题
    this.add.text(400, 120, '斗地主', {
      fontSize: '72px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 4
    }).setOrigin(0.5)
    
    // 副标题
    this.add.text(400, 180, '线上智力竞技比赛选拔系统', {
      fontSize: '24px',
      color: '#cccccc',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)
  }

  createMenu() {
    const menuItems = [
      { text: '开始游戏', action: 'startGame', image: 'btn_start' },
      { text: '创建房间', action: 'createRoom', image: 'btn_create_room' },
      { text: '加入房间', action: 'enterRoom', image: 'btn_enter_room' },
      { text: '游戏规则', action: 'showRules', image: null }
    ]

    menuItems.forEach((item, index) => {
      const y = 280 + index * 70

      if (item.image && this.textures.exists(item.image)) {
        // 使用图片按钮
        const button = this.add.image(400, y, item.image)
        button.setScale(0.8)
        button.setInteractive()

        button.on('pointerover', () => {
          button.setScale(0.85)
        })

        button.on('pointerout', () => {
          button.setScale(0.8)
        })

        button.on('pointerdown', () => {
          this.handleMenuClick(item.action)
        })
      } else {
        // 使用文字按钮（备用方案）
        const buttonBg = this.add.graphics()
        buttonBg.fillStyle(0x4CAF50)
        buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)

        const button = this.add.text(400, y, item.text, {
          fontSize: '28px',
          color: '#ffffff',
          fontFamily: 'Arial, sans-serif',
          fontStyle: 'bold',
          stroke: '#000000',
          strokeThickness: 2
        }).setOrigin(0.5)

        const interactive = this.add.zone(250, y - 25, 300, 50)
        interactive.setInteractive()

        interactive.on('pointerover', () => {
          buttonBg.clear()
          buttonBg.fillStyle(0x4CAF50, 0.8)
          buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)
          button.setScale(1.05)
        })

        interactive.on('pointerout', () => {
          buttonBg.clear()
          buttonBg.fillStyle(0x4CAF50)
          buttonBg.fillRoundedRect(250, y - 25, 300, 50, 10)
          button.setScale(1)
        })

        interactive.on('pointerdown', () => {
          this.handleMenuClick(item.action)
        })
      }
    })
  }

  createFooter() {
    this.add.text(400, 570, '版本 1.0.0 | 开发中...', {
      fontSize: '16px',
      color: '#666666',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)
  }

  handleMenuClick(action) {
    switch(action) {
      case 'startGame':
        console.log('开始游戏')
        this.scene.start('GameScene')
        break
      case 'createRoom':
        this.showMessage('创建房间功能开发中...')
        break
      case 'enterRoom':
        this.showMessage('加入房间功能开发中...')
        break
      case 'showRules':
        this.showRules()
        break
    }
  }

  showRules() {
    // 创建规则弹窗
    const overlay = this.add.graphics()
    overlay.fillStyle(0x000000, 0.7)
    overlay.fillRect(0, 0, 800, 600)
    
    const panel = this.add.graphics()
    panel.fillStyle(0xffffff)
    panel.fillRoundedRect(100, 100, 600, 400, 10)
    panel.lineStyle(2, 0x333333)
    panel.strokeRoundedRect(100, 100, 600, 400, 10)
    
    this.add.text(400, 130, '游戏规则', {
      fontSize: '32px',
      color: '#333333',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const rules = [
      '1. 三人游戏，一人当地主，两人当农民',
      '2. 地主先出牌，然后按逆时针方向轮流出牌',
      '3. 后出牌者必须比前一家出的牌大',
      '4. 最先出完手中牌的一方获胜',
      '5. 炸弹可以压任何牌型，王炸最大'
    ]
    
    rules.forEach((rule, index) => {
      this.add.text(120, 180 + index * 30, rule, {
        fontSize: '18px',
        color: '#333333',
        fontFamily: 'Arial, sans-serif'
      })
    })
    
    // 关闭按钮
    const closeBtn = this.add.text(400, 450, '关闭', {
      fontSize: '24px',
      color: '#ffffff',
      backgroundColor: '#f44336',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)
    
    closeBtn.setInteractive()
    closeBtn.on('pointerdown', () => {
      overlay.destroy()
      panel.destroy()
      closeBtn.destroy()
      // 销毁所有规则文本
      this.children.list.forEach(child => {
        if (child.type === 'Text' && child.text.includes('游戏规则')) {
          child.destroy()
        }
      })
    })
  }

  showMessage(text) {
    const message = this.add.text(400, 300, text, {
      fontSize: '24px',
      color: '#ffff00',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#000000',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)
    
    this.time.delayedCall(2000, () => {
      message.destroy()
    })
  }
}
