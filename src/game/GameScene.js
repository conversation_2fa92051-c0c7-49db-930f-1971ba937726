import Phaser from 'phaser'
import MenuScene from './MenuScene'

class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' })
  }

  preload() {
    // 预加载资源
    console.log('GameScene: preload')
  }

  create() {
    console.log('GameScene: create')

    // 设置背景色 - 深绿色牌桌
    this.cameras.main.setBackgroundColor('#0d4f1c')

    this.createGameTable()
    this.createPlayerAreas()
    this.createGameUI()
    this.createCards()
  }

  createGameTable() {
    // 绘制牌桌
    const graphics = this.add.graphics()

    // 牌桌外圈 - 深绿色
    graphics.fillStyle(0x1a5d2e)
    graphics.fillRoundedRect(50, 100, 700, 400, 20)

    // 牌桌内圈 - 稍浅的绿色
    graphics.fillStyle(0x2d7a3f)
    graphics.fillRoundedRect(70, 120, 660, 360, 15)

    // 中央出牌区域
    graphics.fillStyle(0x1a5d2e)
    graphics.fillRoundedRect(250, 200, 300, 200, 10)

    // 添加牌桌标题
    this.add.text(400, 50, '斗地主', {
      fontSize: '36px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)
  }

  createPlayerAreas() {
    // 玩家位置标识
    const playerPositions = [
      { x: 400, y: 550, name: '玩家（南）', color: '#4CAF50' }, // 下方 - 当前玩家
      { x: 100, y: 300, name: '玩家（西）', color: '#FF9800' }, // 左侧
      { x: 700, y: 300, name: '玩家（东）', color: '#2196F3' }  // 右侧
    ]

    playerPositions.forEach((player, index) => {
      // 玩家区域背景
      const graphics = this.add.graphics()
      graphics.fillStyle(parseInt(player.color.replace('#', '0x')))
      graphics.fillRoundedRect(player.x - 60, player.y - 20, 120, 40, 5)

      // 玩家名称
      this.add.text(player.x, player.y, player.name, {
        fontSize: '16px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif'
      }).setOrigin(0.5)

      // 手牌数量显示
      if (index !== 0) { // 其他玩家显示手牌数量
        this.add.text(player.x, player.y + 25, '手牌: 17张', {
          fontSize: '12px',
          color: '#cccccc',
          fontFamily: 'Arial, sans-serif'
        }).setOrigin(0.5)
      }
    })
  }

  createGameUI() {
    // 游戏信息面板
    const infoPanel = this.add.graphics()
    infoPanel.fillStyle(0x333333)
    infoPanel.fillRoundedRect(20, 20, 200, 60, 5)

    this.add.text(30, 30, '当前局数: 1/3', {
      fontSize: '14px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    })

    this.add.text(30, 50, '底分: 1分', {
      fontSize: '14px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    })

    // 返回菜单按钮
    const backBtn = this.add.text(750, 30, '返回菜单', {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: '#f44336',
      padding: { x: 10, y: 5 }
    }).setOrigin(1, 0)

    backBtn.setInteractive()
    backBtn.on('pointerdown', () => {
      this.scene.start('MenuScene')
    })

    // 操作按钮区域
    this.createActionButtons()
  }

  createActionButtons() {
    const buttonY = 520
    const buttons = [
      { text: '叫地主', x: 200, action: 'callLandlord' },
      { text: '不叫', x: 300, action: 'pass' },
      { text: '出牌', x: 400, action: 'playCards' },
      { text: '不要', x: 500, action: 'skip' },
      { text: '托管', x: 600, action: 'auto' }
    ]

    buttons.forEach(btn => {
      const button = this.add.text(btn.x, buttonY, btn.text, {
        fontSize: '18px',
        color: '#ffffff',
        backgroundColor: '#666666',
        padding: { x: 15, y: 8 }
      }).setOrigin(0.5)

      button.setInteractive()
      button.on('pointerdown', () => {
        console.log(`${btn.text} 按钮被点击`)
        this.handleButtonClick(btn.action)
      })

      // 鼠标悬停效果
      button.on('pointerover', () => {
        button.setStyle({ backgroundColor: '#888888' })
      })

      button.on('pointerout', () => {
        button.setStyle({ backgroundColor: '#666666' })
      })
    })
  }

  createCards() {
    // 创建当前玩家的手牌（示例）
    const cardWidth = 30
    const cardHeight = 45
    const startX = 200
    const cardY = 480

    // 模拟17张手牌
    for (let i = 0; i < 17; i++) {
      const card = this.add.graphics()
      card.fillStyle(0xffffff)
      card.fillRoundedRect(startX + i * 20, cardY, cardWidth, cardHeight, 3)
      card.lineStyle(1, 0x000000)
      card.strokeRoundedRect(startX + i * 20, cardY, cardWidth, cardHeight, 3)

      // 添加简单的牌面标识
      this.add.text(startX + i * 20 + 15, cardY + 22, '♠', {
        fontSize: '12px',
        color: '#000000',
        fontFamily: 'Arial, sans-serif'
      }).setOrigin(0.5)
    }

    // 中央出牌区域提示
    this.add.text(400, 300, '等待叫地主...', {
      fontSize: '20px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)
  }

  handleButtonClick(action) {
    switch(action) {
      case 'callLandlord':
        this.showMessage('您叫了地主！')
        break
      case 'pass':
        this.showMessage('您选择不叫')
        break
      case 'playCards':
        this.showMessage('请选择要出的牌')
        break
      case 'skip':
        this.showMessage('您选择不要')
        break
      case 'auto':
        this.showMessage('已开启托管模式')
        break
    }
  }

  showMessage(text) {
    // 显示临时消息
    const message = this.add.text(400, 150, text, {
      fontSize: '24px',
      color: '#ffff00',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#000000',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)

    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy()
    })
  }
}

// 游戏初始化函数
export default function initGame() {
  const container = document.getElementById('game-container')
  if (!container) {
    throw new Error('Game container element not found')
  }

  const config = {
    type: Phaser.AUTO,
    parent: 'game-container',
    width: 800,
    height: 600,
    scene: [MenuScene, GameScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0 },
        debug: false
      }
    }
  }

  const game = new Phaser.Game(config)
  return game
}
