import Phaser from 'phaser'

class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' })
  }

  preload() {
    // 预加载资源
    console.log('GameScene: preload')
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameScene: create')

    // 设置背景色
    this.cameras.main.setBackgroundColor('#2c5530')

    // 添加游戏标题
    this.add.text(400, 200, '斗地主游戏', {
      fontSize: '48px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)

    // 添加提示文字
    this.add.text(400, 300, '游戏正在开发中...', {
      fontSize: '24px',
      color: '#cccccc',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)

    // 添加一个简单的交互按钮
    const button = this.add.text(400, 400, '点击测试', {
      fontSize: '32px',
      color: '#ffffff',
      backgroundColor: '#4CAF50',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5)

    button.setInteractive()
    button.on('pointerdown', () => {
      console.log('按钮被点击了!')
      button.setText('已点击!')
    })
  }
}

// 游戏初始化函数
export default function initGame() {
  const container = document.getElementById('game-container')
  if (!container) {
    throw new Error('Game container element not found')
  }

  const config = {
    type: Phaser.AUTO,
    parent: 'game-container',
    width: 800,
    height: 600,
    scene: GameScene,
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0 },
        debug: false
      }
    }
  }

  const game = new Phaser.Game(config)
  return game
}
