// 卡牌管理器
export default class CardManager {
  constructor(scene) {
    this.scene = scene
    this.cardSprites = new Map() // 存储卡牌精灵
    this.selectedCards = [] // 选中的卡牌
    this.cardWidth = 50
    this.cardHeight = 70
  }

  // 创建卡牌精灵
  createCard(card, x, y, isClickable = false) {
    const cardSprite = this.scene.add.container(x, y)
    
    // 卡牌背景
    const cardBg = this.scene.add.graphics()
    cardBg.fillStyle(0xffffff)
    cardBg.fillRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 5)
    cardBg.lineStyle(2, 0x333333)
    cardBg.strokeRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 5)
    
    // 卡牌花色和数字
    const color = (card.suit === '♠' || card.suit === '♣') ? '#000000' : '#ff0000'
    
    // 花色
    const suitText = this.scene.add.text(-15, -20, card.suit, {
      fontSize: '16px',
      color: color,
      fontFamily: 'Arial, sans-serif'
    })
    
    // 数字/字母
    const rankText = this.scene.add.text(-15, -5, card.rank, {
      fontSize: card.rank.length > 1 ? '10px' : '14px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })
    
    // 大王小王特殊处理
    if (card.rank === '小王' || card.rank === '大王') {
      suitText.setText('')
      rankText.setText(card.display)
      rankText.setPosition(0, 0)
      rankText.setOrigin(0.5)
      rankText.setStyle({ fontSize: '24px', color: card.rank === '大王' ? '#ff0000' : '#000000' })
    }
    
    cardSprite.add([cardBg, suitText, rankText])
    cardSprite.cardData = card
    cardSprite.isSelected = false
    
    if (isClickable) {
      cardSprite.setSize(this.cardWidth, this.cardHeight)
      cardSprite.setInteractive()
      
      cardSprite.on('pointerdown', () => {
        this.toggleCardSelection(cardSprite)
      })
      
      cardSprite.on('pointerover', () => {
        if (!cardSprite.isSelected) {
          cardSprite.setScale(1.05)
        }
      })
      
      cardSprite.on('pointerout', () => {
        if (!cardSprite.isSelected) {
          cardSprite.setScale(1)
        }
      })
    }
    
    this.cardSprites.set(card.id, cardSprite)
    return cardSprite
  }

  // 切换卡牌选中状态
  toggleCardSelection(cardSprite) {
    if (cardSprite.isSelected) {
      // 取消选中
      cardSprite.isSelected = false
      cardSprite.y += 10
      cardSprite.setScale(1)
      
      const index = this.selectedCards.findIndex(c => c.id === cardSprite.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      // 选中
      cardSprite.isSelected = true
      cardSprite.y -= 10
      cardSprite.setScale(1.1)
      
      this.selectedCards.push(cardSprite.cardData)
    }
    
    // 触发选中状态变化事件
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 清除所有选中状态
  clearSelection() {
    this.cardSprites.forEach(cardSprite => {
      if (cardSprite.isSelected) {
        cardSprite.isSelected = false
        cardSprite.y += 10
        cardSprite.setScale(1)
      }
    })
    this.selectedCards = []
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 显示玩家手牌
  displayPlayerCards(player, x, y, isCurrentPlayer = false) {
    // 清除之前的卡牌
    this.clearPlayerCards(player.id)
    
    const cardSpacing = 35
    const startX = x - (player.cards.length * cardSpacing) / 2
    
    player.cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y, isCurrentPlayer)
      cardSprite.playerId = player.id
    })
  }

  // 显示其他玩家手牌（背面）
  displayOtherPlayerCards(player, x, y, isVertical = false) {
    this.clearPlayerCards(player.id)
    
    const cardCount = player.cards.length
    const spacing = isVertical ? 15 : 25
    
    for (let i = 0; i < cardCount; i++) {
      const cardSprite = this.scene.add.container(
        isVertical ? x : x + i * spacing - (cardCount * spacing) / 2,
        isVertical ? y + i * spacing - (cardCount * spacing) / 2 : y
      )
      
      // 卡牌背面
      const cardBg = this.scene.add.graphics()
      cardBg.fillStyle(0x0066cc)
      cardBg.fillRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 5)
      cardBg.lineStyle(2, 0x003366)
      cardBg.strokeRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 5)
      
      // 背面图案
      const pattern = this.scene.add.text(0, 0, '🂠', {
        fontSize: '30px',
        color: '#ffffff'
      }).setOrigin(0.5)
      
      cardSprite.add([cardBg, pattern])
      cardSprite.playerId = player.id
      
      this.cardSprites.set(`back_${player.id}_${i}`, cardSprite)
    }
  }

  // 清除指定玩家的卡牌
  clearPlayerCards(playerId) {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (sprite.playerId === playerId || key.startsWith(`back_${playerId}_`)) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示出牌区域的卡牌
  displayPlayedCards(cards, x, y) {
    // 清除之前的出牌
    this.clearPlayedCards()
    
    if (!cards || cards.length === 0) return
    
    const cardSpacing = 35
    const startX = x - (cards.length * cardSpacing) / 2
    
    cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.8)
      this.cardSprites.set(`played_${index}`, cardSprite)
    })
  }

  // 清除出牌区域
  clearPlayedCards() {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (key.startsWith('played_')) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示地主牌
  displayLandlordCards(cards, x, y) {
    if (!cards || cards.length === 0) return
    
    cards.forEach((card, index) => {
      const cardX = x + index * 40 - 40
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.7)
      this.cardSprites.set(`landlord_${index}`, cardSprite)
    })
  }

  // 获取选中的卡牌
  getSelectedCards() {
    return [...this.selectedCards]
  }

  // 销毁所有卡牌
  destroy() {
    this.cardSprites.forEach(sprite => sprite.destroy())
    this.cardSprites.clear()
    this.selectedCards = []
  }
}
