// 游戏逻辑管理器
export default class GameLogic {
  constructor() {
    this.reset()
  }

  reset() {
    this.players = [
      { id: 0, name: '玩家（南）', position: 'south', cards: [], isLandlord: false, score: 0 },
      { id: 1, name: '玩家（西）', position: 'west', cards: [], isLandlord: false, score: 0 },
      { id: 2, name: '玩家（东）', position: 'east', cards: [], isLandlord: false, score: 0 }
    ]
    this.landlordCards = [] // 地主牌（3张）
    this.currentPlayer = 0 // 当前操作玩家
    this.gamePhase = 'dealing' // dealing, calling, playing, ended
    this.callHistory = [] // 叫地主历史
    this.playHistory = [] // 出牌历史
    this.landlordId = -1 // 地主ID
    this.baseScore = 1 // 底分
    this.multiplier = 1 // 倍数
    this.lastPlayedCards = null // 上次出的牌
    this.lastPlayerId = -1 // 上次出牌的玩家
  }

  // 生成一副完整的牌
  generateDeck() {
    const suits = ['♠', '♥', '♣', '♦']
    const ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']
    const deck = []

    // 生成普通牌
    suits.forEach(suit => {
      ranks.forEach(rank => {
        deck.push({
          suit,
          rank,
          value: this.getCardValue(rank),
          display: `${suit}${rank}`,
          id: `${suit}${rank}`
        })
      })
    })

    // 添加大小王
    deck.push({ suit: '', rank: '小王', value: 14, display: '🃏', id: 'joker_small' })
    deck.push({ suit: '', rank: '大王', value: 15, display: '🃟', id: 'joker_big' })

    return deck
  }

  // 获取牌的数值（用于比较大小）
  getCardValue(rank) {
    const values = {
      '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
    }
    return values[rank] || 0
  }

  // 洗牌
  shuffleDeck(deck) {
    const shuffled = [...deck]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  // 发牌
  dealCards() {
    const deck = this.shuffleDeck(this.generateDeck())
    
    // 先留出3张地主牌
    this.landlordCards = deck.slice(0, 3)
    
    // 剩余51张牌分给3个玩家，每人17张
    let cardIndex = 3
    for (let i = 0; i < 17; i++) {
      this.players.forEach(player => {
        if (cardIndex < deck.length) {
          player.cards.push(deck[cardIndex++])
        }
      })
    }

    // 对每个玩家的手牌排序
    this.players.forEach(player => {
      player.cards.sort((a, b) => a.value - b.value)
    })

    this.gamePhase = 'calling'
    this.currentPlayer = 1 // 西家先叫
    
    return {
      players: this.players,
      landlordCards: this.landlordCards,
      currentPlayer: this.currentPlayer,
      gamePhase: this.gamePhase
    }
  }

  // 叫地主
  callLandlord(playerId, points) {
    if (this.gamePhase !== 'calling' || playerId !== this.currentPlayer) {
      return { success: false, message: '不是您的叫牌时间' }
    }

    const call = { playerId, points, playerName: this.players[playerId].name }
    this.callHistory.push(call)

    if (points === 3) {
      // 叫3分直接成为地主
      this.landlordId = playerId
      this.players[playerId].isLandlord = true
      this.players[playerId].cards.push(...this.landlordCards)
      this.players[playerId].cards.sort((a, b) => a.value - b.value)
      this.multiplier = points
      this.gamePhase = 'playing'
      this.currentPlayer = playerId // 地主先出牌
      
      return {
        success: true,
        message: `${this.players[playerId].name} 叫了3分，成为地主！`,
        landlordId: this.landlordId,
        gamePhase: this.gamePhase,
        currentPlayer: this.currentPlayer,
        landlordCards: this.landlordCards
      }
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3

    // 检查是否所有人都叫过了
    if (this.callHistory.length === 3) {
      const maxCall = this.callHistory.reduce((max, call) => 
        call.points > max.points ? call : max, { points: 0 })
      
      if (maxCall.points > 0) {
        this.landlordId = maxCall.playerId
        this.players[maxCall.playerId].isLandlord = true
        this.players[maxCall.playerId].cards.push(...this.landlordCards)
        this.players[maxCall.playerId].cards.sort((a, b) => a.value - b.value)
        this.multiplier = maxCall.points
        this.gamePhase = 'playing'
        this.currentPlayer = maxCall.playerId
        
        return {
          success: true,
          message: `${this.players[maxCall.playerId].name} 成为地主！`,
          landlordId: this.landlordId,
          gamePhase: this.gamePhase,
          currentPlayer: this.currentPlayer,
          landlordCards: this.landlordCards
        }
      } else {
        // 重新发牌
        this.reset()
        return this.dealCards()
      }
    }

    return {
      success: true,
      message: points > 0 ? `${this.players[playerId].name} 叫了${points}分` : `${this.players[playerId].name} 不叫`,
      currentPlayer: this.currentPlayer,
      callHistory: this.callHistory
    }
  }

  // 验证牌型
  validateCardType(cards) {
    if (!cards || cards.length === 0) return null

    const sortedCards = [...cards].sort((a, b) => a.value - b.value)
    const length = cards.length

    // 单张
    if (length === 1) {
      return { type: 'single', value: sortedCards[0].value, length: 1 }
    }

    // 对子
    if (length === 2 && sortedCards[0].value === sortedCards[1].value) {
      return { type: 'pair', value: sortedCards[0].value, length: 2 }
    }

    // 三张
    if (length === 3 && sortedCards[0].value === sortedCards[1].value && 
        sortedCards[1].value === sortedCards[2].value) {
      return { type: 'triple', value: sortedCards[0].value, length: 3 }
    }

    // 炸弹
    if (length === 4 && sortedCards[0].value === sortedCards[1].value && 
        sortedCards[1].value === sortedCards[2].value && 
        sortedCards[2].value === sortedCards[3].value) {
      return { type: 'bomb', value: sortedCards[0].value, length: 4 }
    }

    // 王炸
    if (length === 2 && 
        ((sortedCards[0].value === 14 && sortedCards[1].value === 15) ||
         (sortedCards[0].value === 15 && sortedCards[1].value === 14))) {
      return { type: 'rocket', value: 16, length: 2 }
    }

    // 顺子（5张以上连续）
    if (length >= 5) {
      let isSequence = true
      for (let i = 1; i < length; i++) {
        if (sortedCards[i].value !== sortedCards[i-1].value + 1) {
          isSequence = false
          break
        }
      }
      if (isSequence && sortedCards[0].value < 15) { // 不能包含2和王
        return { type: 'sequence', value: sortedCards[0].value, length }
      }
    }

    return null // 无效牌型
  }

  // 比较牌型大小
  compareCards(cards1, cards2) {
    const type1 = this.validateCardType(cards1)
    const type2 = this.validateCardType(cards2)

    if (!type1 || !type2) return false

    // 王炸最大
    if (type1.type === 'rocket') return true
    if (type2.type === 'rocket') return false

    // 炸弹大于其他牌型
    if (type1.type === 'bomb' && type2.type !== 'bomb') return true
    if (type2.type === 'bomb' && type1.type !== 'bomb') return false

    // 同类型比较
    if (type1.type === type2.type && type1.length === type2.length) {
      return type1.value > type2.value
    }

    return false
  }

  // 出牌
  playCards(playerId, selectedCards) {
    if (this.gamePhase !== 'playing' || playerId !== this.currentPlayer) {
      return { success: false, message: '不是您的出牌时间' }
    }

    // 验证牌型
    const cardType = this.validateCardType(selectedCards)
    if (!cardType) {
      return { success: false, message: '无效的牌型' }
    }

    // 检查是否能压过上家的牌
    if (this.lastPlayedCards && this.lastPlayerId !== playerId) {
      if (!this.compareCards(selectedCards, this.lastPlayedCards)) {
        return { success: false, message: '您的牌压不过上家' }
      }
    }

    // 从玩家手牌中移除选中的牌
    const player = this.players[playerId]
    selectedCards.forEach(card => {
      const index = player.cards.findIndex(c => c.id === card.id)
      if (index !== -1) {
        player.cards.splice(index, 1)
      }
    })

    // 记录出牌
    this.lastPlayedCards = selectedCards
    this.lastPlayerId = playerId
    this.playHistory.push({
      playerId,
      cards: selectedCards,
      cardType,
      playerName: player.name
    })

    // 检查是否获胜
    if (player.cards.length === 0) {
      this.gamePhase = 'ended'
      return {
        success: true,
        message: `${player.name} 获胜！`,
        gamePhase: this.gamePhase,
        winner: playerId,
        isLandlordWin: player.isLandlord
      }
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3

    return {
      success: true,
      message: `${player.name} 出了 ${cardType.type}`,
      currentPlayer: this.currentPlayer,
      lastPlayedCards: this.lastPlayedCards,
      playHistory: this.playHistory,
      remainingCards: player.cards.length
    }
  }

  // 不要（跳过出牌）
  skipTurn(playerId) {
    if (this.gamePhase !== 'playing' || playerId !== this.currentPlayer) {
      return { success: false, message: '不是您的操作时间' }
    }

    // 如果是自己出的牌，不能跳过
    if (this.lastPlayerId === playerId) {
      return { success: false, message: '您必须出牌' }
    }

    this.currentPlayer = (this.currentPlayer + 1) % 3

    // 检查是否一圈都不要了
    let skipCount = 0
    for (let i = this.playHistory.length - 1; i >= 0; i--) {
      if (this.playHistory[i].cards) break
      skipCount++
    }

    if (skipCount >= 2) {
      // 清空上次出牌记录，下个玩家可以随意出牌
      this.lastPlayedCards = null
      this.lastPlayerId = -1
    }

    return {
      success: true,
      message: `${this.players[playerId].name} 不要`,
      currentPlayer: this.currentPlayer
    }
  }

  // 获取游戏状态
  getGameState() {
    return {
      players: this.players,
      currentPlayer: this.currentPlayer,
      gamePhase: this.gamePhase,
      landlordId: this.landlordId,
      callHistory: this.callHistory,
      playHistory: this.playHistory,
      lastPlayedCards: this.lastPlayedCards,
      baseScore: this.baseScore,
      multiplier: this.multiplier
    }
  }
}
