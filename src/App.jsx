import { useEffect, useState } from 'react'
import { Button, message } from 'antd'
import GameScene from './game/GameScene'
import './App.css'

export default function App() {
  const [gameReady, setGameReady] = useState(false)

  const handleStartGame = () => {
    setGameReady(true)
  }

  useEffect(() => {
    if (gameReady) {
      // 等待DOM更新后再初始化游戏
      setTimeout(() => {
        try {
          new GameScene()
        } catch (error) {
          console.error('Game initialization error:', error)
          message.error('游戏初始化失败，请刷新页面重试')
          setGameReady(false) // 重置状态，允许重试
        }
      }, 100)
    }
  }, [gameReady])

  return (
    <div className="app-container">
      {!gameReady ? (
        <div className="home-page">
          <h1>斗地主游戏</h1>
          <Button type="primary" size="large" onClick={handleStartGame}>
            开始游戏
          </Button>
        </div>
      ) : (
        <div id="game-container"></div>
      )}
    </div>
  )
}
