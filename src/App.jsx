import { useEffect, useState } from 'react'
import { Button, message } from 'antd'
import initGame from './game/GameScene'
import './App.css'

export default function App() {
  const [gameReady, setGameReady] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleStartGame = () => {
    setLoading(true)
    setGameReady(true)
  }

  const handleBackToHome = () => {
    setGameReady(false)
    setLoading(false)
    // 清理游戏实例
    const container = document.getElementById('game-container')
    if (container) {
      container.innerHTML = ''
    }
  }

  useEffect(() => {
    if (gameReady) {
      // 等待DOM更新后再初始化游戏
      setTimeout(() => {
        try {
          const game = initGame()
          setLoading(false)

          // 监听游戏事件
          window.gameInstance = game

        } catch (error) {
          console.error('Game initialization error:', error)
          message.error('游戏初始化失败，请刷新页面重试')
          setGameReady(false)
          setLoading(false)
        }
      }, 100)
    }
  }, [gameReady])

  // 监听键盘事件
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'Escape' && gameReady) {
        handleBackToHome()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => {
      window.removeEventListener('keydown', handleKeyPress)
    }
  }, [gameReady])

  return (
    <div className="app-container">
      {!gameReady ? (
        <div className="home-page">
          <div className="welcome-content">
            <h1>🃏 斗地主游戏</h1>
            <p className="subtitle">线上智力竞技比赛选拔系统</p>
            <div className="features">
              <div className="feature-item">✨ 经典斗地主玩法</div>
              <div className="feature-item">🎮 流畅游戏体验</div>
              <div className="feature-item">🏆 竞技排位系统</div>
            </div>
            <Button
              type="primary"
              size="large"
              onClick={handleStartGame}
              loading={loading}
              className="start-button"
            >
              {loading ? '正在加载...' : '开始游戏'}
            </Button>
            <div className="tips">
              <p>💡 提示：游戏中按 ESC 键可返回主页</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="game-wrapper">
          <div id="game-container"></div>
          {loading && (
            <div className="loading-overlay">
              <div className="loading-content">
                <div className="loading-spinner"></div>
                <p>游戏加载中...</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
