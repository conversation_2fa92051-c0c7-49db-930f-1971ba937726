.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a472a 0%, #2d7a3f 100%);
  font-family: 'Arial', sans-serif;
}

.home-page {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
}

.welcome-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #1a472a;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
  font-weight: 300;
}

.features {
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-item {
  padding: 12px 20px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 25px;
  font-size: 1.1rem;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
}

.start-button {
  font-size: 1.5rem !important;
  height: 60px !important;
  padding: 0 40px !important;
  border-radius: 30px !important;
  margin: 2rem 0 !important;
  background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  border: none !important;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
  transition: all 0.3s ease !important;
}

.start-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.5) !important;
}

.tips {
  margin-top: 1.5rem;
  padding: 15px;
  background: #f0f8f0;
  border-radius: 10px;
  border-left: 4px solid #4CAF50;
}

.tips p {
  margin: 0;
  color: #2d7a3f;
  font-size: 0.9rem;
}

.game-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

#game-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #333;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  font-size: 1.2rem;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-page {
    padding: 20px;
    margin: 20px;
  }

  .welcome-content h1 {
    font-size: 2.5rem;
  }

  .start-button {
    font-size: 1.3rem !important;
    height: 50px !important;
    padding: 0 30px !important;
  }
}
