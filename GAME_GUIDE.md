# 🃏 斗地主游戏操作指南

## 🎮 游戏流程

### 1. 发牌阶段
- 游戏开始时自动发牌
- 每位玩家获得17张牌
- 剩余3张作为地主牌

### 2. 叫地主阶段
- 从西家（左侧玩家）开始叫地主
- 可以选择：1分、2分、3分、不叫
- 叫3分直接成为地主
- 所有人叫完后，最高分者成为地主
- 如果都不叫，重新发牌

### 3. 出牌阶段
- 地主获得3张底牌，总共20张牌
- 地主先出牌
- 按逆时针方向轮流出牌
- 后出牌者必须压过前一家的牌
- 最先出完牌的一方获胜

## 🎯 操作说明

### 叫地主阶段
- **1分/2分/3分按钮**: 叫对应分数的地主
- **不叫按钮**: 选择不叫地主

### 出牌阶段
- **点击卡牌**: 选择/取消选择卡牌
- **出牌按钮**: 出选中的卡牌
- **不要按钮**: 跳过本轮出牌
- **提示按钮**: 查看选中卡牌的牌型信息

### 通用操作
- **返回菜单**: 退出当前游戏
- **ESC键**: 快速返回主菜单

## 🃏 牌型说明

### 基础牌型
- **单张**: 任意一张牌
- **对子**: 两张相同点数的牌
- **三张**: 三张相同点数的牌
- **顺子**: 5张以上连续的牌（不包括2和王）

### 特殊牌型
- **炸弹**: 四张相同点数的牌
- **王炸**: 大王+小王，最大的牌型

### 牌的大小
- **数字牌**: 3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2
- **王牌**: 小王 < 大王
- **特殊**: 王炸 > 炸弹 > 其他牌型

## 🎲 游戏规则

### 出牌规则
1. 必须出相同类型的牌型
2. 后出的牌必须比前一家大
3. 炸弹可以压任何非炸弹牌型
4. 王炸可以压任何牌型

### 胜负判定
- **地主胜利**: 地主先出完所有牌
- **农民胜利**: 任意一个农民先出完所有牌

### AI行为
- AI会自动进行叫地主和出牌
- AI使用简单策略：
  - 30%概率叫地主
  - 70%概率出牌（优先出最小的单张）
  - 30%概率跳过

## 🎨 界面说明

### 玩家位置
- **下方（南）**: 人类玩家，可以操作
- **左侧（西）**: AI玩家，自动操作
- **右侧（东）**: AI玩家，自动操作

### 信息显示
- **左上角**: 游戏信息（局数、底分、阶段）
- **玩家名称**: 显示当前操作玩家（⭐）和地主（👑）
- **手牌数量**: 显示每位玩家的剩余手牌数
- **中央区域**: 游戏消息和出牌区域

## 🔧 开发功能

### 已实现功能
- ✅ 完整的发牌系统
- ✅ 叫地主逻辑
- ✅ 基础出牌功能
- ✅ 牌型验证
- ✅ AI自动对战
- ✅ 胜负判定
- ✅ 游戏重新开始

### 待优化功能
- 🔄 更智能的AI算法
- 🔄 更多牌型支持（三带一、飞机等）
- 🔄 音效和动画
- 🔄 网络对战功能
- 🔄 游戏统计和排行榜

## 🐛 已知问题

1. AI策略较为简单，可能出现不合理的出牌
2. 部分复杂牌型（如三带一、飞机）暂未实现
3. 缺少出牌动画效果
4. 没有音效反馈

## 💡 使用技巧

1. **选牌技巧**: 点击卡牌会向上移动表示选中
2. **牌型提示**: 选中卡牌后点击"提示"查看牌型
3. **快速操作**: 使用键盘ESC快速返回菜单
4. **观察AI**: 注意观察AI的出牌模式，制定策略

## 🎯 下一步计划

1. **增强AI**: 实现更智能的出牌策略
2. **完善牌型**: 添加三带一、飞机、连对等牌型
3. **视觉效果**: 添加出牌动画和特效
4. **音效系统**: 添加背景音乐和操作音效
5. **网络功能**: 实现真人在线对战

---

**祝您游戏愉快！** 🎉
