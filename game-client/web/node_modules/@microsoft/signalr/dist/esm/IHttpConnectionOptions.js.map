{"version": 3, "file": "IHttpConnectionOptions.js", "sourceRoot": "", "sources": ["../../src/IHttpConnectionOptions.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport } from \"./ITransport\";\r\nimport { EventSourceConstructor, WebSocketConstructor } from \"./Polyfills\";\r\n\r\n/** Options provided to the 'withUrl' method on {@link @microsoft/signalr.HubConnectionBuilder} to configure options for the HTTP-based transports. */\r\nexport interface IHttpConnectionOptions {\r\n    /** {@link @microsoft/signalr.MessageHeaders} containing custom headers to be sent with every HTTP request. Note, setting headers in the browser will not work for WebSockets or the ServerSentEvents stream. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** An {@link @microsoft/signalr.HttpClient} that will be used to make HTTP requests. */\r\n    httpClient?: HttpClient;\r\n\r\n    /** An {@link @microsoft/signalr.HttpTransportType} value specifying the transport to use for the connection. */\r\n    transport?: HttpTransportType | ITransport;\r\n\r\n    /** Configures the logger used for logging.\r\n     *\r\n     * Provide an {@link @microsoft/signalr.ILogger} instance, and log messages will be logged via that instance. Alternatively, provide a value from\r\n     * the {@link @microsoft/signalr.LogLevel} enumeration and a default logger which logs to the Console will be configured to log messages of the specified\r\n     * level (or higher).\r\n     */\r\n    logger?: ILogger | LogLevel;\r\n\r\n    /** A function that provides an access token required for HTTP Bearer authentication.\r\n     *\r\n     * @returns {string | Promise<string>} A string containing the access token, or a Promise that resolves to a string containing the access token.\r\n     */\r\n    accessTokenFactory?(): string | Promise<string>;\r\n\r\n    /** A boolean indicating if message content should be logged.\r\n     *\r\n     * Message content can contain sensitive user data, so this is disabled by default.\r\n     */\r\n    logMessageContent?: boolean;\r\n\r\n    /** A boolean indicating if negotiation should be skipped.\r\n     *\r\n     * Negotiation can only be skipped when the {@link @microsoft/signalr.IHttpConnectionOptions.transport} property is set to 'HttpTransportType.WebSockets'.\r\n     */\r\n    skipNegotiation?: boolean;\r\n\r\n    // Used for unit testing and code spelunkers\r\n    /** A constructor that can be used to create a WebSocket.\r\n     *\r\n     * @internal\r\n     */\r\n    WebSocket?: WebSocketConstructor;\r\n\r\n    // Used for unit testing and code spelunkers\r\n    /** A constructor that can be used to create an EventSource.\r\n     *\r\n     * @internal\r\n     */\r\n    EventSource?: EventSourceConstructor;\r\n\r\n    /**\r\n     * Default value is 'true'.\r\n     * This controls whether credentials such as cookies are sent in cross-site requests.\r\n     *\r\n     * Cookies are used by many load-balancers for sticky sessions which is required when your app is deployed with multiple servers.\r\n     */\r\n    withCredentials?: boolean;\r\n\r\n    /**\r\n     * Default value is 100,000 milliseconds.\r\n     * Timeout to apply to Http requests.\r\n     *\r\n     * This will not apply to Long Polling poll requests, EventSource, or WebSockets.\r\n     */\r\n    timeout?: number;\r\n}\r\n"]}