{"version": 3, "file": "XhrHttpClient.js", "sourceRoot": "", "sources": ["../../src/XhrHttpClient.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAe,YAAY,EAAE,MAAM,cAAc,CAAC;AACrE,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC,MAAM,OAAO,aAAc,SAAQ,UAAU;IAGzC,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,kBAAkB;IACX,IAAI,CAAC,OAAoB;QAC5B,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;YAEjC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAO,EAAE,OAAO,CAAC,GAAI,EAAE,IAAI,CAAC,CAAC;YAC9C,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC7F,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YAC3D,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,EAAE;gBACxB,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC;aAC/B;YACD,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,mFAAmF;gBACnF,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAChC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;iBACpE;qBAAM;oBACH,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;iBACpE;aACJ;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;qBACf,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBAChB,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;aACV;YAED,IAAI,OAAO,CAAC,YAAY,EAAE;gBACtB,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;aAC3C;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC/B,GAAG,CAAC,KAAK,EAAE,CAAC;oBACZ,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;gBAC7B,CAAC,CAAC;aACL;YAED,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aACjC;YAED,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;gBACd,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;iBACtC;gBAED,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;oBACvC,OAAO,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;iBAC3F;qBAAM;oBACH,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;iBACzF;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,4BAA4B,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;gBACjG,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC;YAEF,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                } else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n"]}