{"version": 3, "file": "AccessTokenHttpClient.js", "sourceRoot": "", "sources": ["../../src/AccessTokenHttpClient.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,UAAU,EAA6B,MAAM,cAAc,CAAC;AAErE,eAAe;AACf,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IAKjD,YAAY,WAAuB,EAAE,kBAAgE;QACjG,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,OAAoB;QAClC,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC7G,oHAAoH;YACpH,UAAU,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACxD;QACD,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvD,IAAI,UAAU,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACvE,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YACtC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChD;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,OAAoB;QAChD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,UAAU,IAAI,CAAC,YAAY,EAAE,CAAA;SAC7E;QACD,kHAAkH;aAC7G,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC/B,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE;gBAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;aACrD;SACJ;IACL,CAAC;IAEM,eAAe,CAAC,GAAW;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\n\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    private _innerClient: HttpClient;\r\n    _accessToken: string | undefined;\r\n    _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n\r\n    constructor(innerClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined) {\r\n        super();\r\n\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n\r\n    private _setAuthorizationHeader(request: HttpRequest) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}"]}