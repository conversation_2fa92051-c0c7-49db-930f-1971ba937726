{"version": 3, "file": "HubConnection.js", "sourceRoot": "", "sources": ["../../src/HubConnection.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,iBAAiB,EAAqD,MAAM,qBAAqB,CAAC;AAE3G,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAA+E,WAAW,EAA8C,MAAM,gBAAgB,CAAC;AACtK,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAG9C,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAExD,MAAM,qBAAqB,GAAW,EAAE,GAAG,IAAI,CAAC;AAChD,MAAM,2BAA2B,GAAW,EAAE,GAAG,IAAI,CAAC;AAEtD,8EAA8E;AAC9E,MAAM,CAAN,IAAY,kBAWX;AAXD,WAAY,kBAAkB;IAC1B,0CAA0C;IAC1C,mDAA6B,CAAA;IAC7B,wCAAwC;IACxC,+CAAyB,CAAA;IACzB,uCAAuC;IACvC,6CAAuB,CAAA;IACvB,2CAA2C;IAC3C,qDAA+B,CAAA;IAC/B,0CAA0C;IAC1C,mDAA6B,CAAA;AACjC,CAAC,EAXW,kBAAkB,KAAlB,kBAAkB,QAW7B;AAED,gDAAgD;AAChD,MAAM,OAAO,aAAa;IAmEtB,YAAoB,UAAuB,EAAE,MAAe,EAAE,QAAsB,EAAE,eAA8B;QAvC5G,mBAAc,GAAW,CAAC,CAAC;QAS3B,yBAAoB,GAAG,GAAG,EAAE;YAEhC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,sNAAsN,CAAC,CAAC;QAC/P,CAAC,CAAC;QA4BE,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,2BAA2B,GAAG,qBAAqB,CAAC;QACzD,IAAI,CAAC,+BAA+B,GAAG,2BAA2B,CAAC;QAEnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE3E,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;IACtF,CAAC;IArCD,gBAAgB;IAChB,iGAAiG;IACjG,+FAA+F;IAC/F,6FAA6F;IAC7F,qCAAqC;IAC9B,MAAM,CAAC,MAAM,CAAC,UAAuB,EAAE,MAAe,EAAE,QAAsB,EAAE,eAA8B;QACjH,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;IAC5E,CAAC;IAgCD,sEAAsE;IACtE,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3E,CAAC;IAED,oEAAoE;IACpE,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO,CAAC,GAAW;QACnB,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YACxH,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC,CAAC;SAC7G;QAED,IAAI,CAAC,GAAG,EAAE;YACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACpC,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YAC3D,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC,CAAC;SAC7G;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,UAAU,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;QAE5D,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE5B,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,uGAAuG;gBACvG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACzE;YAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,CAAC;YACrD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uCAAuC,CAAC,CAAC;SAC7E;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,gEAAgE,CAAC,IAAI,CAAC,CAAC;YACxG,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC5B;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,yGAAyG;QACzG,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE3D,IAAI;YACA,MAAM,gBAAgB,GAA4B;gBAC9C,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBAC7B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;aAClC,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,4BAA4B,CAAC,CAAC;YAE/D,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEzF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,sBAAsB,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC;YAEtF,kGAAkG;YAClG,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,MAAM,gBAAgB,CAAC;YAEvB,mGAAmG;YACnG,6GAA6G;YAC7G,iCAAiC;YACjC,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,4GAA4G;gBAC5G,+GAA+G;gBAC/G,qEAAqE;gBACrE,+DAA+D;gBAC/D,MAAM,IAAI,CAAC,qBAAqB,CAAC;aACpC;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE;gBAC7C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACpD;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,oCAAoC,CAAC,2CAA2C,CAAC,CAAC;YAEnH,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,yFAAyF;YACzF,2GAA2G;YAC3G,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,CAAC;SACX;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,IAAI;QACb,6FAA6F;QAC7F,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QAExC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACzC,MAAM,IAAI,CAAC,YAAY,CAAC;QAExB,IAAI;YACA,2CAA2C;YAC3C,MAAM,YAAY,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;YACR,sFAAsF;SACzF;IACL,CAAC;IAEO,aAAa,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,8BAA8B,KAAK,4DAA4D,CAAC,CAAC;YAClI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,+BAA+B,KAAK,yEAAyE,CAAC,CAAC;YAChJ,OAAO,IAAI,CAAC,YAAa,CAAC;SAC7B;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,aAAa,CAAC;QAEzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,iGAAiG;YACjG,+FAA+F;YAC/F,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,+DAA+D,CAAC,CAAC;YAElG,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAEvC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,qBAAqB,GAAG,KAAK,IAAI,IAAI,UAAU,CAAC,qEAAqE,CAAC,CAAC;QAE5H,4FAA4F;QAC5F,6FAA6F;QAC7F,+EAA+E;QAC/E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAU,UAAkB,EAAE,GAAG,IAAW;QACrD,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAEvF,wCAAwC;QACxC,IAAI,YAA2B,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,OAAO,EAAK,CAAC;QACjC,OAAO,CAAC,cAAc,GAAG,GAAG,EAAE;YAC1B,MAAM,gBAAgB,GAA4B,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAElH,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAE1D,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,eAA6D,EAAE,KAAa,EAAE,EAAE;YAClI,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO;aACV;iBAAM,IAAI,eAAe,EAAE;gBACxB,+EAA+E;gBAC/E,IAAI,eAAe,CAAC,IAAI,KAAK,WAAW,CAAC,UAAU,EAAE;oBACjD,IAAI,eAAe,CAAC,KAAK,EAAE;wBACvB,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;qBACnD;yBAAM;wBACH,OAAO,CAAC,QAAQ,EAAE,CAAC;qBACtB;iBACJ;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAM,CAAC,CAAC;iBAC7C;aACJ;QACL,CAAC,CAAC;QAEF,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;aACtD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEP,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,OAAY;QAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACK,iBAAiB,CAAC,OAAY;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG;IACI,IAAI,CAAC,UAAkB,EAAE,GAAG,IAAW;QAC1C,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;QAEtG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAE1C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAU,UAAkB,EAAE,GAAG,IAAW;QACrD,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAExF,MAAM,CAAC,GAAG,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,sEAAsE;YACtE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAa,CAAC,GAAG,CAAC,eAA6D,EAAE,KAAa,EAAE,EAAE;gBACnI,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,CAAC,CAAC;oBACd,OAAO;iBACV;qBAAM,IAAI,eAAe,EAAE;oBACxB,+EAA+E;oBAC/E,IAAI,eAAe,CAAC,IAAI,KAAK,WAAW,CAAC,UAAU,EAAE;wBACjD,IAAI,eAAe,CAAC,KAAK,EAAE;4BACvB,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC5C;6BAAM;4BACH,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;yBACnC;qBACJ;yBAAM;wBACH,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;qBACzE;iBACJ;YACL,CAAC,CAAC;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;iBAC5D,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBACT,MAAM,CAAC,CAAC,CAAC,CAAC;gBACV,sEAAsE;gBACtE,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAa,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAEP,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC;IACb,CAAC;IAQM,EAAE,CAAC,UAAkB,EAAE,SAAmC;QAC7D,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE;YAC3B,OAAO;SACV;QAED,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAClC;QAED,qDAAqD;QACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YACrD,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAiBM,GAAG,CAAC,UAAkB,EAAE,MAAiC;QAC5D,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO;SACV;QACD,IAAI,MAAM,EAAE;YACR,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAClB,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;iBACpC;aACJ;SACJ;aAAM;YACH,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACpC;IAEL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAiC;QAC5C,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,QAAiC;QACnD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,QAAyC;QAC1D,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC7C;IACL,CAAC;IAEO,oBAAoB,CAAC,IAAS;QAClC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClC,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SAC1C;QAED,iEAAiE;QACjE,IAAI,IAAI,EAAE;YACN,qBAAqB;YACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAElE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC5B,QAAQ,OAAO,CAAC,IAAI,EAAE;oBAClB,KAAK,WAAW,CAAC,UAAU;wBACvB,mEAAmE;wBACnE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM;oBACV,KAAK,WAAW,CAAC,UAAU,CAAC;oBAC5B,KAAK,WAAW,CAAC,UAAU,CAAC,CAAC;wBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBACvD,IAAI,QAAQ,EAAE;4BACV,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,UAAU,EAAE;gCACzC,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;6BAChD;4BACD,IAAI;gCACA,QAAQ,CAAC,OAAO,CAAC,CAAC;6BACrB;4BAAC,OAAO,CAAC,EAAE;gCACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,gCAAgC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;6BACzF;yBACJ;wBACD,MAAM;qBACT;oBACD,KAAK,WAAW,CAAC,IAAI;wBACjB,yBAAyB;wBACzB,MAAM;oBACV,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC;wBACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,qCAAqC,CAAC,CAAC;wBAE9E,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,qCAAqC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;wBAE3G,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;4BACjC,6IAA6I;4BAC7I,4HAA4H;4BAE5H,mEAAmE;4BACnE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAC/B;6BAAM;4BACH,0HAA0H;4BAC1H,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;yBACjD;wBAED,MAAM;qBACT;oBACD;wBACI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,yBAAyB,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;wBAC7E,MAAM;iBACb;aACJ;SACJ;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,yBAAyB,CAAC,IAAS;QACvC,IAAI,eAAyC,CAAC;QAC9C,IAAI,aAAkB,CAAC;QAEvB,IAAI;YACA,CAAC,aAAa,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SAC3F;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,OAAO,GAAG,oCAAoC,GAAG,CAAC,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE1C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;SACf;QACD,IAAI,eAAe,CAAC,KAAK,EAAE;YACvB,MAAM,OAAO,GAAG,mCAAmC,GAAG,eAAe,CAAC,KAAK,CAAC;YAC5E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE1C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;SACf;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,4BAA4B,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,uBAAuB;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,OAAO;SACV;QAED,sDAAsD;QACtD,8CAA8C;QAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,+BAA+B,CAAC;QAElF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YAC1E,wBAAwB;YACxB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAE/F,yCAAyC;YACzC,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EACxC;gBACI,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;gBAC1D,IAAI,QAAQ,GAAG,CAAC,EAAE;oBACd,QAAQ,GAAG,CAAC,CAAC;iBAChB;gBAED,iIAAiI;gBACjI,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;oBAC3C,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,EAAE;wBACxD,IAAI;4BACA,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;yBACpD;wBAAC,MAAM;4BACJ,4EAA4E;4BAC5E,oGAAoG;4BACpG,IAAI,CAAC,iBAAiB,EAAE,CAAC;yBAC5B;qBACJ;gBACL,CAAC,EAAE,QAAQ,CAAC,CAAC;aAChB;SACJ;IACL,CAAC;IAED,gEAAgE;IACxD,aAAa;QACjB,+EAA+E;QAC/E,uGAAuG;QACvG,mEAAmE;QACnE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;IAC3G,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,iBAAoC;QAClE,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,mCAAmC,UAAU,UAAU,CAAC,CAAC;YAE5F,mGAAmG;YACnG,IAAI,iBAAiB,CAAC,YAAY,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,wBAAwB,UAAU,+BAA+B,iBAAiB,CAAC,YAAY,IAAI,CAAC,CAAC;gBACxI,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,YAAY,EAAE,iCAAiC,EAAE,IAAI,CAAC,CAAC,CAAC;aACxI;YACD,OAAO;SACV;QAED,oGAAoG;QACpG,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAEpC,4BAA4B;QAC5B,MAAM,eAAe,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACtE,uEAAuE;QACvE,IAAI,GAAG,CAAC;QACR,IAAI,SAAS,CAAC;QACd,IAAI,iBAAiB,CAAC;QACtB,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE;YACzB,IAAI;gBACA,MAAM,OAAO,GAAG,GAAG,CAAC;gBACpB,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBACvD,IAAI,eAAe,IAAI,GAAG,IAAI,OAAO,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,kCAAkC,UAAU,6BAA6B,CAAC,CAAC;oBAC5G,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,YAAa,EAAE,mCAAmC,EAAE,IAAI,CAAC,CAAC;iBACjI;gBACD,0EAA0E;gBAC1E,SAAS,GAAG,SAAS,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACR,SAAS,GAAG,CAAC,CAAC;gBACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,8BAA8B,UAAU,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACrG;SACJ;QACD,IAAI,iBAAiB,EAAE;YACnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;SACnD;aAAM,IAAI,eAAe,EAAE;YACxB,mGAAmG;YACnG,IAAI,SAAS,EAAE;gBACX,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,YAAa,EAAE,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;aAC5G;iBAAM,IAAI,GAAG,KAAK,SAAS,EAAE;gBAC1B,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,YAAa,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;aACjG;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,wBAAwB,UAAU,+BAA+B,iBAAiB,CAAC,YAAY,IAAI,CAAC,CAAC;gBACxI,wGAAwG;gBACxG,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,YAAa,EAAE,iCAAiC,EAAE,IAAI,CAAC,CAAC;aAC/H;YACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;SACnD;aAAM;YACH,IAAI,GAAG,EAAE;gBACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,qBAAqB,UAAU,gDAAgD,CAAC,CAAC;aACrH;SACJ;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,kCAAkC,KAAK,2BAA2B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE7H,kIAAkI;QAClI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,+EAA+E,CAAC,CAAC;QAEpK,uGAAuG;QACvG,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,yBAAyB,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC,CAAC;QAEzH,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACxF,mEAAmE;YACnE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,EAAE;YAC/D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,gGAAgG;QAChG,sHAAsH;QACtH,kJAAkJ;QAClJ,oDAAoD;QACpD,8DAA8D;IAClE,CAAC;IAEO,cAAc,CAAC,KAAa;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAEhC,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC5E;YAED,IAAI;gBACA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAChE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,0CAA0C,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5G;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAa;QAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,IAAI,yBAAyB,GAAG,CAAC,CAAC;QAClC,IAAI,UAAU,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAE5G,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAEzF,IAAI,cAAc,KAAK,IAAI,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,oGAAoG,CAAC,CAAC;YACvI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;QAExD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,6CAA6C,KAAK,IAAI,CAAC,CAAC;SAClG;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI;gBACA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,iDAAiD,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACnH;YAED,qEAAqE;YACrE,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uFAAuF,CAAC,CAAC;gBAC1H,OAAO;aACV;SACJ;QAED,OAAO,cAAc,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,4BAA4B,yBAAyB,kBAAkB,cAAc,MAAM,CAAC,CAAC;YAEpI,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,EAAE,cAAe,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAEvC,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,mFAAmF,CAAC,CAAC;gBACtH,OAAO;aACV;YAED,IAAI;gBACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAE5B,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,yCAAyC,CAAC,CAAC;gBAElF,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzC,IAAI;wBACA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;qBAC5F;oBAAC,OAAO,CAAC,EAAE;wBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uDAAuD,IAAI,CAAC,UAAU,CAAC,YAAY,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBAChJ;iBACJ;gBAED,OAAO;aACV;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,8CAA8C,CAAC,IAAI,CAAC,CAAC;gBAE5F,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;oBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,4BAA4B,IAAI,CAAC,gBAAgB,4EAA4E,CAAC,CAAC;oBAChK,gHAAgH;oBAChH,IAAI,IAAI,CAAC,gBAAuB,KAAK,kBAAkB,CAAC,aAAa,EAAE;wBACnE,IAAI,CAAC,cAAc,EAAE,CAAC;qBACzB;oBACD,OAAO;iBACV;gBAED,UAAU,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC9D,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB,EAAE,UAAU,CAAC,CAAC;aACtH;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,+CAA+C,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB,WAAW,yBAAyB,6CAA6C,CAAC,CAAC;QAExM,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,kBAAkB,CAAC,kBAA0B,EAAE,mBAA2B,EAAE,WAAkB;QAClG,IAAI;YACA,OAAO,IAAI,CAAC,gBAAiB,CAAC,4BAA4B,CAAC;gBACvD,mBAAmB;gBACnB,kBAAkB;gBAClB,WAAW;aACd,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,6CAA6C,kBAAkB,KAAK,mBAAmB,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjJ,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEO,yBAAyB,CAAC,KAAY;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aACjB,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI;gBACA,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,wCAAwC,KAAK,kBAAkB,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACxH;QACL,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;SACtC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrC;IACL,CAAC;IAEO,iBAAiB,CAAC,UAAkB,EAAE,IAAW,EAAE,WAAoB,EAAE,SAAmB;QAChG,IAAI,WAAW,EAAE;YACb,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,SAAS;oBACT,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,WAAW,CAAC,UAAU;iBAC/B,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,WAAW,CAAC,UAAU;iBAC/B,CAAC;aACL;SACJ;aAAM;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;oBACrC,SAAS;oBACT,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,WAAW,CAAC,UAAU;iBAC/B,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;oBACrC,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,WAAW,CAAC,UAAU;iBAC/B,CAAC;aACL;SACJ;IACL,CAAC;IAEO,cAAc,CAAC,OAA6B,EAAE,YAA2B;QAC7E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO;SACV;QAED,gEAAgE;QAChE,IAAI,CAAC,YAAY,EAAE;YACf,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;SACpC;QAED,sEAAsE;QACtE,wCAAwC;QACxC,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE;YAC5B,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;gBACxB,QAAQ,EAAE,GAAG,EAAE;oBACX,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5G,CAAC;gBACD,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE;oBACX,IAAI,OAAe,CAAC;oBACpB,IAAI,GAAG,YAAY,KAAK,EAAE;wBACtB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;qBACzB;yBAAM,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE;wBAC5B,OAAO,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;qBAC5B;yBAAM;wBACH,OAAO,GAAG,eAAe,CAAC;qBAC7B;oBAED,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;gBACrH,CAAC;gBACD,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;oBACX,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClH,CAAC;aACJ,CAAC,CAAC;SACN;IACL,CAAC;IAEO,uBAAuB,CAAC,IAAW;QACvC,MAAM,OAAO,GAAyB,EAAE,CAAC;QACzC,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;gBACpC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,iCAAiC;gBACjC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;gBAC7B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAEpC,0BAA0B;gBAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrB;SACJ;QAED,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAEO,aAAa,CAAC,GAAQ;QAC1B,oEAAoE;QACpE,OAAO,GAAG,IAAI,GAAG,CAAC,SAAS,IAAI,OAAO,GAAG,CAAC,SAAS,KAAK,UAAU,CAAC;IACvE,CAAC;IAEO,uBAAuB,CAAC,UAAkB,EAAE,IAAW,EAAE,SAAmB;QAChF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO;gBACH,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;gBACrC,SAAS;gBACT,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,WAAW,CAAC,gBAAgB;aACrC,CAAC;SACL;aAAM;YACH,OAAO;gBACH,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;gBACrC,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,WAAW,CAAC,gBAAgB;aACrC,CAAC;SACL;IACL,CAAC;IAEO,uBAAuB,CAAC,EAAU;QACtC,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,IAAI,EAAE,WAAW,CAAC,gBAAgB;SACrC,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,EAAU,EAAE,IAAS;QAClD,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,IAAI;YACJ,IAAI,EAAE,WAAW,CAAC,UAAU;SAC/B,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,EAAU,EAAE,KAAW,EAAE,MAAY;QAClE,IAAI,KAAK,EAAE;YACP,OAAO;gBACH,KAAK;gBACL,YAAY,EAAE,EAAE;gBAChB,IAAI,EAAE,WAAW,CAAC,UAAU;aAC/B,CAAC;SACL;QAED,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,MAAM;YACN,IAAI,EAAE,WAAW,CAAC,UAAU;SAC/B,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { CancelInvocationMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: (((...args: any[]) => void) | ((...args: any[]) => any))[] };\r\n    private _invocationId: number;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://docs.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy);\r\n    }\r\n\r\n    private constructor(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version: this._protocol.version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        return this._sendMessage(this._protocol.writeMessage(message));\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => any): void\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                        this._invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private async _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            } catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `${exception}`, null);\r\n            } else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, null, res);\r\n            } else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n}\r\n"]}