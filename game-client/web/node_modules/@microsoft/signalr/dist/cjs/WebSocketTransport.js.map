{"version": 3, "file": "WebSocketTransport.js", "sourceRoot": "", "sources": ["../../src/WebSocketTransport.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,+CAA4C;AAG5C,uCAA8C;AAC9C,6CAA0D;AAE1D,mCAA2E;AAE3E,eAAe;AACf,MAAa,kBAAkB;IAY3B,YAAY,UAAsB,EAAE,kBAAgE,EAAE,MAAe,EACzG,iBAA0B,EAAE,oBAA0C,EAAE,OAAuB;QACvG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,WAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,WAAG,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,WAAG,CAAC,IAAI,CAAC,cAAc,EAAE,2BAAc,EAAE,gBAAgB,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oCAAoC,CAAC,CAAC;QAEvE,IAAI,KAAa,CAAC;QAClB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5C;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACjC,IAAI,SAAgC,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,IAAI,gBAAQ,CAAC,MAAM,IAAI,gBAAQ,CAAC,aAAa,EAAE;gBAC3C,MAAM,OAAO,GAA0B,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,0BAAkB,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBACtB,IAAI,KAAK,EAAE;oBACP,OAAO,CAAC,yBAAW,CAAC,aAAa,CAAC,GAAG,UAAU,KAAK,EAAE,CAAC;iBAC1D;gBAED,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,yBAAW,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;iBACzC;gBAED,qDAAqD;gBACrD,SAAS,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,EAAE;oBACvD,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;iBAC5C,CAAC,CAAC;aACN;iBAED;gBACI,IAAI,KAAK,EAAE;oBACP,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;iBAC3F;aACJ;YAED,IAAI,CAAC,SAAS,EAAE;gBACZ,2DAA2D;gBAC3D,SAAS,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;aACnD;YAED,IAAI,cAAc,KAAK,2BAAc,CAAC,MAAM,EAAE;gBAC1C,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;aACxC;YAED,SAAS,CAAC,MAAM,GAAG,CAAC,MAAa,EAAE,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,WAAW,EAAE,0BAA0B,GAAG,GAAG,CAAC,CAAC;gBACzE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC;gBACd,OAAO,EAAE,CAAC;YACd,CAAC,CAAC;YAEF,SAAS,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;gBACjC,IAAI,KAAK,GAAQ,IAAI,CAAC;gBACtB,wFAAwF;gBACxF,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,KAAK,YAAY,UAAU,EAAE;oBAClE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;iBACvB;qBAAM;oBACH,KAAK,GAAG,uCAAuC,CAAC;iBACnD;gBAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,WAAW,EAAE,0BAA0B,KAAK,GAAG,CAAC,CAAC;YAC/E,CAAC,CAAC;YAEF,SAAS,CAAC,SAAS,GAAG,CAAC,OAAqB,EAAE,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,yCAAyC,qBAAa,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBACnI,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI;wBACA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;qBAChC;oBAAC,OAAO,KAAK,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACnB,OAAO;qBACV;iBACJ;YACL,CAAC,CAAC;YAEF,SAAS,CAAC,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;gBACtC,+DAA+D;gBAC/D,wCAAwC;gBACxC,IAAI,MAAM,EAAE;oBACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBACtB;qBAAM;oBACH,IAAI,KAAK,GAAQ,IAAI,CAAC;oBACtB,wFAAwF;oBACxF,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,KAAK,YAAY,UAAU,EAAE;wBAClE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;qBACvB;yBAAM;wBACH,KAAK,GAAG,+EAA+E;8BACrF,qDAAqD;8BACrD,2FAA2F;8BAC3F,uEAAuE,CAAC;qBAC7E;oBAED,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC5B;YACL,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,IAAI,CAAC,IAAS;QACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE;YACnF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,wCAAwC,qBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAC1H,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;IAChE,CAAC;IAEM,IAAI;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,6GAA6G;YAC7G,iHAAiH;YACjH,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,KAA0B;QACrC,qEAAqE;QACrE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,4EAA4E;YAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC/B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,uCAAuC,CAAC,CAAC;QAC1E,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChF,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,iBAAiB,IAAI,CAAC,CAAC,CAAC;aACvH;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;SACJ;IACL,CAAC;IAEO,aAAa,CAAC,KAAW;QAC7B,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IAC1F,CAAC;CACJ;AA/KD,gDA+KC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        let token: string;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else\r\n            {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event?: CloseEvent | Error): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n"]}