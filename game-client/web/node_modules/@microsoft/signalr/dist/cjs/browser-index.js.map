{"version": 3, "file": "browser-index.js", "sourceRoot": "", "sources": ["../../src/browser-index.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;;;;;;;;;;AAEvE,qHAAqH;AAErH,uIAAuI;AACvI,wEAAwE;AACxE,8EAA8E;AAC9E,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;IAC/B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE;QACnD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO;QAC9B,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AACD,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE;IAC7B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;QACjD,wEAAwE;QACxE,4CAA4C;QAC5C,KAAK,EAAE,UAAS,KAAc,EAAE,GAAY,IAAI,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtH,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AACD,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;IAC/B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE;QACnD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO;QAC9B,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AAED,0CAAwB", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\n// Copy from Array.prototype into Uint8Array to polyfill on IE. It's OK because the implementations of indexOf and slice use properties\r\n// that exist on Uint8Array with the same name, and JavaScript is magic.\r\n// We make them 'writable' because the Buffer polyfill messes with it as well.\r\nif (!Uint8Array.prototype.indexOf) {\r\n    Object.defineProperty(Uint8Array.prototype, \"indexOf\", {\r\n        value: Array.prototype.indexOf,\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.slice) {\r\n    Object.defineProperty(Uint8Array.prototype, \"slice\", {\r\n        // wrap the slice in Uint8Array so it looks like a Uint8Array.slice call\r\n        // eslint-disable-next-line object-shorthand\r\n        value: function(start?: number, end?: number) { return new Uint8Array(Array.prototype.slice.call(this, start, end)); },\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.forEach) {\r\n    Object.defineProperty(Uint8Array.prototype, \"forEach\", {\r\n        value: Array.prototype.forEach,\r\n        writable: true,\r\n    });\r\n}\r\n\r\nexport * from \"./index\";\r\n"]}