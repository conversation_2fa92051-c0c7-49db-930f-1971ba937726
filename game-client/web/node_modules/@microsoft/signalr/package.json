{"name": "@microsoft/signalr", "version": "7.0.14", "description": "ASP.NET Core SignalR Client", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "typings": "./dist/esm/index.d.ts", "umd": "./dist/browser/signalr.js", "umd_name": "signalR", "unpkg": "./dist/browser/signalr.js", "directories": {"test": "spec"}, "sideEffects": false, "scripts": {"preclean": "cd ../common && yarn install --mutex network --frozen-lockfile", "clean": "node ../common/node_modules/rimraf/bin.js ./dist", "prebuild": "yarn run clean && yarn install --mutex network --frozen-lockfile", "build": "yarn run build:lint && yarn run build:esm && yarn run build:cjs && yarn run build:browser && yarn run build:webworker", "build:lint": "node ../common/node_modules/eslint/bin/eslint ./src --ext .ts --resolve-plugins-relative-to ../common", "build:esm": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module es2015 --outDir ./dist/esm -d && node ./build/process-dts.js", "build:cjs": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module commonjs --outDir ./dist/cjs", "build:browser": "node ../common/node_modules/webpack-cli/bin/cli.js", "build:webworker": "node ../common/node_modules/webpack-cli/bin/cli.js --env platform=webworker", "prepack": "node ../build/embed-version.js", "test": "echo \"Run 'yarn test' in the 'clients/ts' folder to test this package\" && exit 1"}, "keywords": ["signalr", "aspnetcore"], "repository": {"type": "git", "url": "git+https://github.com/dotnet/aspnetcore.git"}, "author": "Microsoft", "license": "MIT", "bugs": {"url": "https://github.com/dotnet/aspnetcore/issues"}, "homepage": "https://github.com/dotnet/aspnetcore/tree/main/src/SignalR#readme", "files": ["dist/**/*", "src/**/*"], "devDependencies": {"@types/eventsource": "^1.1.8", "@types/jest": "^26.0.20", "@types/node": "^14.14.31", "@types/tough-cookie": "^4.0.0", "process": "^0.11.10"}, "dependencies": {"abort-controller": "^3.0.0", "eventsource": "^2.0.2", "fetch-cookie": "^2.0.3", "node-fetch": "^2.6.7", "ws": "^7.4.5"}, "resolutions": {"ansi-regex": "5.0.1"}}