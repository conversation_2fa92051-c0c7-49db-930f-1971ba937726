{"name": "fetch-cookie", "version": "2.2.0", "description": "Decorator for a `fetch` function to support automatic cookies.", "license": "Unlicense", "author": "Val (https://val.codejam.info)", "type": "module", "main": "./cjs/index-wrapper.js", "module": "./esm/index.js", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}, "require": {"types": "./cjs/index.d.ts", "default": "./cjs/index-wrapper.js"}}}, "types": "esm/index.d.ts", "repository": "valeriangalliat/fetch-cookie", "scripts": {"build": "esbuild src/*.ts --format=esm --outdir=esm && esbuild src/*.ts --format=cjs --outdir=cjs", "lint": "ts-standard src/*.ts test/*.js", "patch-undici": "sed -i.old 's/^const forbiddenResponseHeaderNames =.*$/const forbiddenResponseHeaderNames = []/' node_modules/undici/lib/fetch/constants.js", "prepack": "npm run prepare && npm run lint && npm run type-check && npm test && npm run test-import", "prepare": "npm run build && npm run type-declarations", "test": "npm run build && mocha", "test-import": "node test/cjs/import.js && ts-node test/cjs/import.ts", "type-check": "tsc -noEmit", "type-declarations": "tsc --project tsconfig.build.json --declaration --emitDeclarationOnly --outDir esm && cat esm/index.d.ts | sed 's/^export default /export = /' > cjs/index.d.ts"}, "dependencies": {"set-cookie-parser": "^2.4.8", "tough-cookie": "^4.0.0"}, "devDependencies": {"@tsconfig/recommended": "^1.0.1", "@types/chai": "^4.3.0", "@types/express": "^4.17.13", "@types/mocha": "^9.1.0", "@types/node": "^17.0.18", "@types/node-fetch": "^2.6.1", "@types/set-cookie-parser": "^2.4.2", "@types/tough-cookie": "^4.0.1", "chai": "^4.3.6", "esbuild": "^0.14.21", "express": "^4.17.3", "mocha": "^9.2.0", "node-fetch": "^3.2.0", "ts-node": "^10.7.0", "ts-standard": "^11.0.0", "typescript": "^4.5.5", "undici": "^4.14.1"}}